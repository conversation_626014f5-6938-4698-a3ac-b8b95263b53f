import os
import shutil
from datetime import datetime, timedelta
from pathlib import Path
import calendar

def cleanup_previous_month_files(cache_dir="D:\\影刀缓存"):
    """
    清除上个月的所有文件（保留文件夹结构）
    每月20号执行，清除上个月1日至月末的所有文件
    """
    today = datetime.now()
    
    # 检查是否是20号
    if today.day != 20:
        print(f"今天是{today.day}号，不是20号，不执行清理")
        return
    
    # 计算上个月的日期范围
    if today.month == 1:
        prev_month = 12
        prev_year = today.year - 1
    else:
        prev_month = today.month - 1
        prev_year = today.year
    
    # 上个月第一天和最后一天
    first_day = datetime(prev_year, prev_month, 1)
    last_day_num = calendar.monthrange(prev_year, prev_month)[1]
    last_day = datetime(prev_year, prev_month, last_day_num, 23, 59, 59)
    
    print(f"开始清理 {prev_year}年{prev_month}月 的文件...")
    print(f"清理时间范围: {first_day.strftime('%Y-%m-%d')} 至 {last_day.strftime('%Y-%m-%d')}")
    
    cache_path = Path(cache_dir)
    if not cache_path.exists():
        print(f"缓存目录不存在: {cache_dir}")
        return
    
    deleted_count = 0
    
    # 遍历所有文件（包括子文件夹中的文件）
    for root, dirs, files in os.walk(cache_dir):
        for file in files:
            file_path = Path(root) / file
            try:
                # 获取文件修改时间
                file_mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                
                # 检查文件是否在上个月的时间范围内
                if first_day <= file_mtime <= last_day:
                    file_path.unlink()  # 删除文件
                    deleted_count += 1
                    print(f"已删除: {file_path}")
                    
            except Exception as e:
                print(f"删除文件失败 {file_path}: {e}")
    
    print(f"清理完成，共删除 {deleted_count} 个文件")

def cleanup_empty_directories(cache_dir="D:\\影刀缓存"):
    """
    清理空的子文件夹
    """
    cache_path = Path(cache_dir)
    removed_dirs = []
    
    # 从最深层开始，自底向上删除空文件夹
    for root, dirs, files in os.walk(cache_dir, topdown=False):
        for dir_name in dirs:
            dir_path = Path(root) / dir_name
            try:
                if not any(dir_path.iterdir()):  # 检查文件夹是否为空
                    dir_path.rmdir()
                    removed_dirs.append(str(dir_path))
                    print(f"已删除空文件夹: {dir_path}")
            except Exception as e:
                print(f"删除空文件夹失败 {dir_path}: {e}")
    
    if removed_dirs:
        print(f"共删除 {len(removed_dirs)} 个空文件夹")
    else:
        print("没有发现空文件夹")

if __name__ == "__main__":
    # 执行清理
    cleanup_previous_month_files()
    
    # 可选：清理空文件夹
    cleanup_empty_directories()