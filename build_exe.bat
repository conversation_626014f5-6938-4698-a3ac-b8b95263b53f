@echo off
chcp 65001 >nul
echo ========================================
echo 正在编译 cleanup_cache.py 为 exe 文件
echo ========================================

echo.
echo 清理之前的编译文件...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
if exist "*.spec" del /q "*.spec"

echo.
echo 开始编译...
pyinstaller --onefile --console --name "清空影刀缓存" cleanup_cache.py

echo.
if exist "dist\清空影刀缓存.exe" (
    echo ========================================
    echo 编译成功！
    echo ========================================
    echo 可执行文件位置: dist\清空影刀缓存.exe
    echo.
    echo 正在打开文件夹...
    explorer dist
) else (
    echo ========================================
    echo 编译失败！
    echo ========================================
    echo 请检查错误信息
)

echo.
pause
